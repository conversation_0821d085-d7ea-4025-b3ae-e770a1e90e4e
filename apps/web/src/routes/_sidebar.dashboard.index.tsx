import * as React from "react"
import { createFileRoute } from "@tanstack/react-router"
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import type { ColumnDef, ColumnFiltersState, SortingState, VisibilityState } from "@tanstack/react-table"
import { Download } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

import { columns } from "@/features/tasks/components/columns"
import { DataTable } from "@/features/tasks/components/data-table"
import { taskSchema } from "@/features/tasks/data/schema"
import tasks from "@/features/tasks/data/tasks.json"

export const Route = createFileRoute("/_sidebar/dashboard/")({
  component: RouteComponent,
  head: () => ({
    meta: [
      {
        title: "Dashboard | Platform Meeting",
      },
      {
        name: "description",
        content: "Dashboard page for Platform Meeting",
      },
    ],
  }),
})

function RouteComponent() {
  return (
    <div className="flex flex-col space-y-4 py-4">
      <div className="flex justify-between px-4 lg:px-6">
        <h1 className="text-3xl font-bold">Platform Meeting</h1>

        <Button>
          <Download className="size-4" />
          <span>Export</span>
        </Button>
      </div>

      <DataTable columns={columns} data={tasks} />
    </div>
  )
}
