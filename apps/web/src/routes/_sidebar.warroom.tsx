import { createFileRoute } from "@tanstack/react-router"
import { Download } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"

import { DataTable } from "@/features/outlines/components/data-table"
import outlines from "@/features/outlines/data/outlines.json"

export const Route = createFileRoute("/_sidebar/warroom")({
  component: RouteComponent,
})

function RouteComponent() {
  return (
    <div className="flex flex-col space-y-4 py-4">
      <div className="flex justify-between px-4 lg:px-6">
        <h1 className="text-3xl font-bold">Platform Meeting</h1>

        <Button>
          <Download className="size-4" />
          <span>Export</span>
        </Button>
      </div>

      <DataTable data={outlines} />
    </div>
  )
}
