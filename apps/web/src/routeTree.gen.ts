/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as SidebarRouteRouteImport } from './routes/_sidebar.route'
import { Route as IndexRouteImport } from './routes/index'
import { Route as SidebarWarroomRouteImport } from './routes/_sidebar.warroom'
import { Route as SidebarDashboardIndexRouteImport } from './routes/_sidebar.dashboard.index'

const SidebarRouteRoute = SidebarRouteRouteImport.update({
  id: '/_sidebar',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const SidebarWarroomRoute = SidebarWarroomRouteImport.update({
  id: '/warroom',
  path: '/warroom',
  getParentRoute: () => SidebarRouteRoute,
} as any)
const SidebarDashboardIndexRoute = SidebarDashboardIndexRouteImport.update({
  id: '/dashboard/',
  path: '/dashboard/',
  getParentRoute: () => SidebarRouteRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/warroom': typeof SidebarWarroomRoute
  '/dashboard': typeof SidebarDashboardIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/warroom': typeof SidebarWarroomRoute
  '/dashboard': typeof SidebarDashboardIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/_sidebar': typeof SidebarRouteRouteWithChildren
  '/_sidebar/warroom': typeof SidebarWarroomRoute
  '/_sidebar/dashboard/': typeof SidebarDashboardIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/warroom' | '/dashboard'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/warroom' | '/dashboard'
  id:
    | '__root__'
    | '/'
    | '/_sidebar'
    | '/_sidebar/warroom'
    | '/_sidebar/dashboard/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  SidebarRouteRoute: typeof SidebarRouteRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_sidebar': {
      id: '/_sidebar'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof SidebarRouteRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_sidebar/warroom': {
      id: '/_sidebar/warroom'
      path: '/warroom'
      fullPath: '/warroom'
      preLoaderRoute: typeof SidebarWarroomRouteImport
      parentRoute: typeof SidebarRouteRoute
    }
    '/_sidebar/dashboard/': {
      id: '/_sidebar/dashboard/'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof SidebarDashboardIndexRouteImport
      parentRoute: typeof SidebarRouteRoute
    }
  }
}

interface SidebarRouteRouteChildren {
  SidebarWarroomRoute: typeof SidebarWarroomRoute
  SidebarDashboardIndexRoute: typeof SidebarDashboardIndexRoute
}

const SidebarRouteRouteChildren: SidebarRouteRouteChildren = {
  SidebarWarroomRoute: SidebarWarroomRoute,
  SidebarDashboardIndexRoute: SidebarDashboardIndexRoute,
}

const SidebarRouteRouteWithChildren = SidebarRouteRoute._addFileChildren(
  SidebarRouteRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  SidebarRouteRoute: SidebarRouteRouteWithChildren,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
